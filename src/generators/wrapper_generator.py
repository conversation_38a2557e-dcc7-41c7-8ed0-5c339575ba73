# -*- coding: utf-8 -*-
"""
Wrapper Generator - Geração de componentes wrapper via IA.

Este módulo é responsável pela geração de componentes wrapper Angular
usando IA com modelo de analysis, baseado nos metadados salvos.
"""

import json
import glob
from pathlib import Path
from typing import Dict, Any, List
import re

from src.utils.config import ConfigLoader
from src.utils.logging import get_logger
from src.utils.file_operations import FileOperations
from src.utils.angular_utils import generate_angular_component_files
from src.generators.utils.generator_config_loader import GeneratorConfigLoader

logger = get_logger(__name__)

class WrapperGenerator:
    """
    Gera componentes wrapper Angular via IA usando modelo de analysis.
    """
    
    def __init__(self, config_path: str = "project_config.yaml", flow_client=None):
        # Usar o novo utilitário de configuração
        self.config_helper = GeneratorConfigLoader(config_path)
        self.config = self.config_helper.get_config()
        self.ai_config = self.config_helper.get_ai_config()
        self.prompts = self.config_helper.load_prompts()
        self.flow_client = flow_client
        

        


    def generate_wrappers_from_metadata(self, output_dir: str):
        """
        Gera wrappers a partir dos metadados salvos.
        
        Args:
            output_dir: Diretório onde procurar metadados de wrapper
        """
        if not self.flow_client:
            logger.error("❌ Flow API não disponível para geração de wrappers.")
            return
        
        logger.info(f"🔍 Procurando metadados de wrapper em: {output_dir}")
        
        # Procurar por arquivos de metadados de wrapper
        metadata_files = self._find_metadata_files(output_dir)
        
        if not metadata_files:
            logger.info("ℹ️ Nenhum metadado de wrapper encontrado.")
            return
        
        logger.info(f"📦 Encontrados {len(metadata_files)} metadados de wrapper")
        
        for metadata_file in metadata_files:
            try:
                self._generate_single_wrapper(metadata_file)
            except Exception as e:
                logger.error(f"❌ Erro ao gerar wrapper de {metadata_file}: {e}")

    def _find_metadata_files(self, output_dir: str) -> List[str]:
        """
        Encontra arquivos de metadata de wrappers.
        
        Args:
            output_dir: Diretório de saída
            
        Returns:
            Lista de caminhos para arquivos de metadata
        """
        
        # Procurar na pasta figma_processed em vez de angular
        figma_processed_dir = output_dir.replace('/angular/', '/figma_processed/')
        metadata_pattern = f"{figma_processed_dir}/**/*_wrapper_metadata.json"
        
        metadata_files = glob.glob(metadata_pattern, recursive=True)
        
        logger.info(f"🔍 Encontrados {len(metadata_files)} arquivos de metadata de wrapper")
        for file in metadata_files:
            logger.debug(f"   📄 {file}")
        
        return metadata_files

    def _generate_single_wrapper(self, metadata_file: str):
        """
        Gera um wrapper específico a partir do arquivo de metadados.
        
        Args:
            metadata_file: Caminho para o arquivo de metadados
        """
        logger.info(f"🚀 Gerando wrapper: {metadata_file}")
        
        # Carregar metadados usando o utilitário
        metadata = FileOperations.read_json_file(metadata_file)
        if not metadata:
            logger.error(f"❌ Erro ao carregar metadados: {metadata_file}")
            return
        
        wrapper_name = metadata['wrapper_name']
        normalized_name = metadata['normalized_name']
        child_components = metadata['child_components']
        figma_data = metadata['figma_data']
        output_dir = metadata['output_dir']
        
        logger.info(f"📦 Wrapper: {wrapper_name}")
        logger.info(f"   Componentes filhos: {child_components}")
        
        # Preparar contexto para IA
        context = self._prepare_wrapper_context(metadata, output_dir)
        
        # Gerar código via IA usando modelo de analysis
        html = self._generate_wrapper_html_ai(context)
        ts = self._generate_wrapper_typescript_ai(context)
        scss = self._generate_wrapper_scss_ai(context)
        
        # Salvar arquivos do wrapper
        component_dir = generate_angular_component_files(normalized_name, html, ts, scss, output_dir)
        
        # Remover arquivo de metadata após geração
        try:
            Path(metadata_file).unlink()
            logger.info(f"🗑️ Arquivo de metadata removido: {metadata_file}")
        except Exception as e:
            logger.warning(f"⚠️ Erro ao remover arquivo de metadata: {e}")
        
        logger.info(f"✅ Wrapper gerado: {component_dir}")

    def _prepare_wrapper_context(self, metadata: Dict[str, Any], output_dir: str) -> Dict[str, Any]:
        """
        Prepara contexto para geração do wrapper via IA.
        
        Args:
            metadata: Metadados do wrapper
            output_dir: Diretório de saída
            
        Returns:
            Contexto para IA
        """
        wrapper_name = metadata['wrapper_name']
        normalized_name = metadata['normalized_name']
        child_components = metadata['child_components']
        figma_data = metadata['figma_data']
        
        # Coletar informações dos componentes filhos gerados
        child_components_info = []
        for child in child_components:
            child_info = self._get_child_component_info(child, output_dir)
            if child_info:
                child_components_info.append(child_info)
        
        # Contexto para IA
        context = {
            'wrapper_name': wrapper_name,
            'normalized_name': normalized_name,
            'child_components': child_components,
            'child_components_info': child_components_info,
            'figma_data': figma_data,
            'total_children': len(child_components)
        }
        
        logger.debug("📋 Contexto preparado:")
        logger.debug(f"   Wrapper: {wrapper_name}")
        logger.debug(f"   Componentes filhos: {len(child_components)}")
        logger.debug(f"   Informações coletadas: {len(child_components_info)}")
        
        return context

    def _get_child_component_info(self, child_name: str, output_dir: str) -> Dict[str, Any]:
        """
        Coleta informações de um componente filho gerado.
        
        Args:
            child_name: Nome do componente filho
            output_dir: Diretório de saída
            
        Returns:
            Informações do componente filho
        """
        child_dir = Path(output_dir) / child_name
        
        if not child_dir.exists():
            logger.warning(f"⚠️ Diretório do componente filho não encontrado: {child_dir}")
            return None
        
        # Procurar por arquivos do componente
        html_file = child_dir / f"{child_name}.component.html"
        ts_file = child_dir / f"{child_name}.component.ts"
        scss_file = child_dir / f"{child_name}.component.scss"
        
        child_info = {
            'name': child_name,
            'path': str(child_dir),
            'has_html': html_file.exists(),
            'has_ts': ts_file.exists(),
            'has_scss': scss_file.exists()
        }
        
        # Ler conteúdo dos arquivos se existirem usando o utilitário
        if html_file.exists():
            child_info['html_content'] = FileOperations.read_text_file(html_file)

        if ts_file.exists():
            child_info['ts_content'] = FileOperations.read_text_file(ts_file)

        if scss_file.exists():
            child_info['scss_content'] = FileOperations.read_text_file(scss_file)
        
        return child_info

    def _generate_wrapper_html_ai(self, context: Dict[str, Any]) -> str:
        """
        Gera HTML do wrapper via IA usando modelo de analysis.
        
        Args:
            context: Contexto para IA
            
        Returns:
            HTML do wrapper
        """
        if not self.flow_client:
            return self._generate_fallback_wrapper_html(context)
        
        logger.info(f"Gerando HTML do wrapper: {context['wrapper_name']}")
        
        # System prompt do YAML
        system_prompt = self.prompts['system_prompts']['wrapper_html_generation']['role'] + "\n" + \
                       self.prompts['system_prompts']['wrapper_html_generation']['mission'] + "\n" + \
                       self.prompts['system_prompts']['wrapper_html_generation']['guidelines']

        # User prompt do YAML
        user_prompt = self.prompts['user_prompts']['wrapper_html_generation']['task']
        user_prompt += f"\n\nGere o HTML do componente wrapper Angular '{context['wrapper_name']}' que referencia os seguintes componentes filhos:\n\n"
        user_prompt += f"Componentes filhos:\n{self._format_child_components_for_prompt(context['child_components_info'])}\n\n"
        user_prompt += f"Dados do Figma:\n{json.dumps(context['figma_data'], ensure_ascii=False, indent=2)}"

        try:
            model = self.ai_config['model']['analysis']  # Usar modelo de analysis
            temperature = 0.1
            max_tokens = 4000  # Menor para analysis
            
            response = self.flow_client.with_model(model).get_answer(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            return self._sanitize_ai_response(response)
            
        except Exception as e:
            logger.error(f"❌ Erro na geração de HTML do wrapper: {e}")
            return self._generate_fallback_wrapper_html(context)

    def _generate_wrapper_typescript_ai(self, context: Dict[str, Any]) -> str:
        """
        Gera TypeScript do wrapper via IA usando modelo de analysis.
        
        Args:
            context: Contexto para IA
            
        Returns:
            TypeScript do wrapper
        """
        if not self.flow_client:
            return self._generate_fallback_wrapper_typescript(context)
        
        logger.info(f"Gerando TypeScript do wrapper: {context['wrapper_name']}")
        
        # System prompt do YAML
        system_prompt = self.prompts['system_prompts']['wrapper_typescript_generation']['role'] + "\n" + \
                       self.prompts['system_prompts']['wrapper_typescript_generation']['mission'] + "\n" + \
                       self.prompts['system_prompts']['wrapper_typescript_generation']['guidelines']

        # User prompt do YAML
        user_prompt = self.prompts['user_prompts']['wrapper_typescript_generation']['task']
        user_prompt += f"\n\nGere o TypeScript do componente wrapper Angular '{context['wrapper_name']}' que referencia os seguintes componentes filhos:\n\n"
        user_prompt += f"Componentes filhos:\n{self._format_child_components_for_prompt(context['child_components_info'])}\n\n"
        user_prompt += f"Dados do Figma:\n{json.dumps(context['figma_data'], ensure_ascii=False, indent=2)}"

        try:
            model = self.ai_config['model']['analysis']  # Usar modelo de analysis
            temperature = 0.1
            max_tokens = 4000  # Menor para analysis
            
            response = self.flow_client.with_model(model).get_answer(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            return self._sanitize_ai_response(response)
            
        except Exception as e:
            logger.error(f"❌ Erro na geração de TypeScript do wrapper: {e}")
            return self._generate_fallback_wrapper_typescript(context)

    def _generate_wrapper_scss_ai(self, context: Dict[str, Any]) -> str:
        """
        Gera SCSS do wrapper via IA usando modelo de analysis.
        
        Args:
            context: Contexto para IA
            
        Returns:
            SCSS do wrapper
        """
        if not self.flow_client:
            return self._generate_fallback_wrapper_scss(context)
        
        logger.info(f"Gerando SCSS do wrapper: {context['wrapper_name']}")
        
        # System prompt do YAML
        system_prompt = self.prompts['system_prompts']['wrapper_scss_generation']['role'] + "\n" + \
                       self.prompts['system_prompts']['wrapper_scss_generation']['mission'] + "\n" + \
                       self.prompts['system_prompts']['wrapper_scss_generation']['guidelines']

        # User prompt do YAML
        user_prompt = self.prompts['user_prompts']['wrapper_scss_generation']['task']
        user_prompt += f"\n\nGere o SCSS do componente wrapper Angular '{context['wrapper_name']}' baseado nos dados do Figma:\n\n"
        user_prompt += f"Dados CSS do Figma:\n{json.dumps(context['figma_data'].get('css', {}), ensure_ascii=False, indent=2)}\n\n"
        user_prompt += f"Componentes filhos:\n{self._format_child_components_for_prompt(context['child_components_info'])}"

        try:
            model = self.ai_config['model']['analysis']  # Usar modelo de analysis
            temperature = 0.1
            max_tokens = 4000  # Menor para analysis
            
            response = self.flow_client.with_model(model).get_answer(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            return self._sanitize_ai_response(response)
            
        except Exception as e:
            logger.error(f"❌ Erro na geração de SCSS do wrapper: {e}")
            return self._generate_fallback_wrapper_scss(context)

    def _format_child_components_for_prompt(self, child_components_info: List[Dict[str, Any]]) -> str:
        """
        Formata informações dos componentes filhos para o prompt.
        
        Args:
            child_components_info: Lista de informações dos componentes filhos
            
        Returns:
            String formatada para o prompt
        """
        if not child_components_info:
            return "Nenhum componente filho encontrado."
        
        formatted = []
        for i, child in enumerate(child_components_info, 1):
            formatted.append(f"{i}. {child['name']}")
            if child.get('html_content'):
                formatted.append(f"   HTML: {len(child['html_content'])} chars")
            if child.get('ts_content'):
                formatted.append(f"   TS: {len(child['ts_content'])} chars")
            if child.get('scss_content'):
                formatted.append(f"   SCSS: {len(child['scss_content'])} chars")
            formatted.append("")
        
        return "\n".join(formatted)

    def _sanitize_ai_response(self, response: str) -> str:
        """Sanitiza a resposta da IA removendo blocos de código desnecessários."""
        if not response:
            return ""
        
        # Remover apenas os marcadores de início e fim de blocos de código
        sanitized = response
        
        # Remover ```typescript, ```html, ```scss no início de linhas
        sanitized = re.sub(r'^```(?:typescript|html|scss|css|javascript|js|ts|angular|component)\s*$', '', sanitized, flags=re.MULTILINE)
        
        # Remover ``` no final de linhas
        sanitized = re.sub(r'^\s*```\s*$', '', sanitized, flags=re.MULTILINE)
        
        # Remover linhas vazias extras
        sanitized = re.sub(r'\n\s*\n\s*\n', '\n\n', sanitized)
        
        # Remover espaços em branco no início e fim
        sanitized = sanitized.strip()
        
        # Se a resposta estiver vazia após sanitização, retornar fallback
        if not sanitized:
            return "<!-- Conteúdo gerado pela IA -->"
        
        return sanitized

    def _generate_fallback_wrapper_html(self, context: Dict[str, Any]) -> str:
        """Gera HTML de fallback para wrapper."""
        wrapper_name = context['wrapper_name']
        child_components = context['child_components']
        
        html_lines = [f'<!-- Wrapper: {wrapper_name} -->']
        html_lines.append('<div class="wrapper-container">')
        
        for child in child_components:
            html_lines.append(f'  <app-{child}></app-{child}>')
        
        html_lines.append('</div>')
        
        return '\n'.join(html_lines)

    def _generate_fallback_wrapper_typescript(self, context: Dict[str, Any]) -> str:
        """Gera TypeScript de fallback para wrapper."""
        wrapper_name = context['wrapper_name']
        normalized_name = context['normalized_name']
        child_components = context['child_components']
        
        ts_lines = ['import { Component } from \'@angular/core\';']
        
        # Imports dos componentes filhos
        for child in child_components:
            ts_lines.append(f"import {{ {child.title()}Component }} from '../{child}/{child}.component';")
        
        ts_lines.append('')
        ts_lines.append('@Component({')
        ts_lines.append(f"  selector: 'app-{normalized_name}',")
        ts_lines.append(f"  templateUrl: './{normalized_name}.component.html',")
        ts_lines.append(f"  styleUrls: ['./{normalized_name}.component.scss']")
        ts_lines.append('})')
        ts_lines.append('')
        ts_lines.append(f'export class {wrapper_name.replace(" ", "")}Component {{')
        ts_lines.append('  // Wrapper component')
        ts_lines.append('}')
        
        return '\n'.join(ts_lines)

    def _generate_fallback_wrapper_scss(self, context: Dict[str, Any]) -> str:
        """Gera SCSS de fallback para wrapper."""
        wrapper_name = context['wrapper_name']
        
        scss_lines = [f'// Wrapper: {wrapper_name}']
        scss_lines.append('// Erro na geração')
        scss_lines.append('// Implemente o SCSS manualmente')
        
        return '\n'.join(scss_lines) 