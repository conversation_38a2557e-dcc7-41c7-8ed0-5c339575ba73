# -*- coding: utf-8 -*-
"""
Component Generator - Geração final de código Angular.

Este módulo é responsável pela geração final de código Angular a partir dos dados
processados do Figma e mapeamentos com o Design System.
"""

import json
import re
from pathlib import Path
from typing import Dict, Any, List
from dataclasses import dataclass
import tiktoken

# Carregar variáveis de ambiente
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("⚠️ python-dotenv não instalado. Tentando carregar .env manualmente...")
    import os
    env_path = Path(__file__).parent.parent.parent / '.env'
    if env_path.exists():
        with open(env_path, 'r') as f:
            for line in f:
                if line.strip() and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value

from src.utils.config import ConfigLoader
from src.utils.logging import get_logger
from src.utils.angular_utils import (
    normalize_to_kebab_case, 
    generate_angular_component_files
)
from src.utils.figma_utils import (
    load_design_system_colors,
    extract_structure_info
)
from src.generators.utils.figma_data_processor import FigmaDataProcessor
from src.generators.figma_reader import FigmaComponentData
from src.generators.design_system_mapper import DesignSystemMapping
from src.utils.color_mapper import create_color_mapper_from_design_system

logger = get_logger(__name__)

@dataclass
class GeneratedComponent:
    """Componente Angular gerado."""
    name: str
    html_template: str
    typescript_code: str
    scss_styles: str
    metadata: Dict[str, Any]

class ComponentGenerator:
    """
    Gera código final (HTML, TS, SCSS) usando IA, prompts centralizados e templates do Design System.
    """
    def __init__(self, config_path: str = "project_config.yaml", flow_client=None):
        self.config_loader = ConfigLoader(config_path)
        self.config = self.config_loader.load_config()
        self.ai_config = self.config_loader.get_ai_config()
        self.prompts = self._load_prompts()
        self.flow_client = flow_client
        
        # Inicializar processador de dados do Figma
        self.figma_processor = FigmaDataProcessor(flow_client=self.flow_client)

    def _load_prompts(self) -> Dict[str, Any]:
        import yaml
        prompts_path = Path(__file__).parent / "prompts.yaml"
        with open(prompts_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)


    def generate_component_code(self, figma_data: FigmaComponentData, mapping: DesignSystemMapping, output_dir: str):
        """
        Gera HTML, TS e SCSS para o componente Angular completo usando fluxo sequencial.
        """
        logger.info(f"\n🚀 Gerando código final para: {figma_data.component_name}")

        # Verificar se é um componente wrapper
        is_wrapper = self._is_wrapper_component(figma_data)
        child_components = self._get_child_components(figma_data)

        if is_wrapper and child_components:
            logger.info(f"📦 Componente wrapper detectado. Componentes filhos: {child_components}")
            # Para wrappers, apenas salvar metadados para geração posterior
            self._save_wrapper_metadata(figma_data, child_components, output_dir)
            return

        # Pré-processar dados do Figma com IA
        ai_processed_data = self.figma_processor.preprocess_with_ai(figma_data)

        # Preparar contexto base para IA
        base_context = self._prepare_complete_context(figma_data, mapping, ai_processed_data)

        # NOVO FLUXO SEQUENCIAL
        from .utils.sequential_generator import SequentialGenerator
        from .utils.validation_engine import ValidationEngine
        from .utils.ai_analysis_helper import AIAnalysisHelper

        # Inicializar geradores
        sequential_gen = SequentialGenerator(self.flow_client, self.prompts, self.ai_config)
        validation_engine = ValidationEngine()
        ai_helper = AIAnalysisHelper(self.flow_client, self.prompts, self.ai_config)

        # Gerar código em sequência
        html, typescript, scss = sequential_gen.generate_with_context_flow(base_context)

        # Validação e correção automática
        html, typescript, scss = validation_engine.validate_and_fix_all(html, typescript, scss)

        # Validação final com IA
        html, typescript, scss = ai_helper.final_validation_with_ai(html, typescript, scss, base_context)

        # Salvar arquivos do componente Angular
        self._save_component_files(figma_data.normalized_name, html, typescript, scss, output_dir)

    def _save_wrapper_metadata(self, figma_data: FigmaComponentData, child_components: List[str], output_dir: str):
        """
        Salva metadados do wrapper para geração posterior via IA.
        
        Args:
            figma_data: Dados do Figma
            child_components: Lista de componentes filhos
            output_dir: Diretório de saída
        """
        import json
        
        # Normalizar nomes dos componentes filhos
        normalized_children = []
        for child in child_components:
            normalized_children.append(normalize_to_kebab_case(child))
        
        # Criar metadados do wrapper
        wrapper_metadata = {
            'wrapper_name': figma_data.component_name,
            'normalized_name': figma_data.normalized_name,
            'figma_id': figma_data.figma_id,
            'child_components': normalized_children,
            'figma_data': figma_data.__dict__,
            'output_dir': output_dir
        }
        
        # Salvar metadados em arquivo JSON na pasta figma_processed
        figma_processed_dir = output_dir.replace('/angular/', '/figma_processed/')
        metadata_path = Path(figma_processed_dir) / f"{figma_data.normalized_name}_wrapper_metadata.json"
        
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(wrapper_metadata, f, indent=2, ensure_ascii=False)
        
        logger.info(f"💾 Metadados do wrapper salvos: {metadata_path}")
    

    def _prepare_complete_context(self, figma_data: FigmaComponentData, mapping: DesignSystemMapping, ai_processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """Prepara contexto completo para geração do componente Angular."""
        
        logger.info(f"🔍 Preparando contexto para: {figma_data.component_name}")
        logger.debug(f"   Figma ID: {figma_data.figma_id}")
        logger.debug(f"   Dados processados pela IA: {len(ai_processed_data)} seções")
        
        # Mapear webcomponents para templates do Design System
        mapped_components = []
        loaded_templates = set()  # Para detectar duplicatas
        
        for map_item in mapping.mappings:
            if map_item.design_system_component:
                template_file = map_item.design_system_component.file_path
                
                # Verificar se template já foi carregado
                if template_file in loaded_templates:
                    logger.debug(f"   Template duplicado ignorado: {template_file}")
                    continue  # Pular duplicatas
                
                template = self._load_ds_template(template_file)
                loaded_templates.add(template_file)
                
                # Determinar tipo de webcomponent
                webcomponent_type = map_item.figma_webcomponent.get('type', 'confirmed')
                confidence_note = f" (possible: {map_item.confidence:.2f})" if webcomponent_type == 'possible' else f" (confirmed: {map_item.confidence:.2f})"
                logger.debug(f"   Template {len(loaded_templates)}: {template_file}{confidence_note}")
                
                mapped_components.append({
                    "figma": map_item.figma_webcomponent,
                    "design_system": {
                        "name": map_item.design_system_component.name,
                        "description": map_item.design_system_component.description,
                        "template": template,
                        "category": map_item.design_system_component.category
                    },
                    "confidence": map_item.confidence,
                    "properties_mapping": map_item.properties_mapping,
                    "webcomponent_type": webcomponent_type
                })
        
        # Mapear cores do Figma para classes CSS do Design System
        logger.debug("   Mapeando cores do Figma...")
        color_mapping = self._map_figma_colors_to_css_classes(figma_data)
        logger.debug(f"   Cores mapeadas: {len(color_mapping)} classes")
        
        # Extrair informações de estrutura
        logger.debug("   Extraindo informações de estrutura...")
        structure_info = self._extract_structure_info(figma_data)
        
        # Contexto completo para IA (convertido para dict serializável)
        context = {
            "component_name": figma_data.component_name,
            "figma_id": figma_data.figma_id,
            "raw_html": figma_data.html_structure,
            "ai_processed_data": ai_processed_data,
            "mapped_components": mapped_components,
            "total_components": len(mapped_components),
            "component_type": self._detect_component_type(figma_data),
            "color_mapping": color_mapping,
            "structure_info": structure_info,
            "figma_css_styles": figma_data.css_styles,  # Adicionar estilos CSS do Figma
            "metadata": {
                "figma_file": figma_data.metadata.get('figma_file', ''),
                "component_type": figma_data.metadata.get('component_type', ''),
                "webcomponents_count": figma_data.metadata.get('webcomponents_count', 0),
                "has_children": figma_data.metadata.get('has_children', False),
                "generation_method": figma_data.metadata.get('generation_method', ''),
                "total_webcomponents": figma_data.metadata.get('total_webcomponents', 0)
            }
        }
        
        # Adicionar conteúdo específico do header se existir
        if structure_info.get('header_content'):
            logger.debug("   Adicionando conteúdo do header")
            context['header_content'] = structure_info['header_content']
        
        # Adicionar conteúdo específico das actions se existir
        if structure_info.get('actions_content'):
            logger.debug("   Adicionando conteúdo das actions")
            context['actions_content'] = structure_info['actions_content']
        
        # Log resumo do contexto
        logger.debug("Resumo do contexto:")
        logger.debug(f"   Templates carregados: {len(loaded_templates)}")
        logger.debug(f"   Classes de cores: {len(color_mapping)}")
        logger.debug(f"   Seções de estrutura: {len(structure_info)}")
        logger.debug(f"   HTML do Figma: {len(figma_data.html_structure)} chars")
        logger.debug(f"   Dados processados pela IA: {len(ai_processed_data)} seções")
        
        return context

    def _detect_component_type(self, figma_data: FigmaComponentData) -> str:
        """Detecta o tipo do componente baseado no nome e estrutura."""
        name = figma_data.component_name.lower()
        
        if 'modal' in name or 'dialog' in name:
            return 'modal'
        elif 'form' in name or 'input' in name:
            return 'form'
        elif 'button' in name:
            return 'button'
        elif 'card' in name:
            return 'card'
        elif 'table' in name:
            return 'table'
        else:
            return 'component'

    def _is_wrapper_component(self, figma_data: FigmaComponentData) -> bool:
        """Detecta se o componente é um wrapper (arquivo principal)."""
        # Verificar se há outros componentes no metadata
        all_components = figma_data.metadata.get('all_components_names', [])
        current_name = figma_data.component_name
        
        # Se há outros componentes além do atual, este é um wrapper
        other_components = [comp for comp in all_components if comp != current_name]
        
        # O wrapper é sempre o componente principal (primeiro na lista)
        is_main_component = all_components and current_name == all_components[0]
        
        return is_main_component and len(other_components) > 0

    def _get_child_components(self, figma_data: FigmaComponentData) -> List[str]:
        """Obtém a lista de componentes filhos."""
        all_components = figma_data.metadata.get('all_components_names', [])
        current_name = figma_data.component_name
        
        # Retornar todos os componentes exceto o atual
        return [comp for comp in all_components if comp != current_name]

    def _sanitize_ai_response(self, response: str) -> str:
        """Sanitiza a resposta da IA removendo blocos de código desnecessários."""
        if not response:
            return ""
        
        # Remover apenas os marcadores de início e fim de blocos de código
        # Preservar o conteúdo dentro dos blocos
        sanitized = response
        
        # Remover ```typescript, ```html, ```scss no início de linhas
        sanitized = re.sub(r'^```(?:typescript|html|scss|css|javascript|js|ts|angular|component)\s*$', '', sanitized, flags=re.MULTILINE)
        
        # Remover ``` no final de linhas
        sanitized = re.sub(r'^\s*```\s*$', '', sanitized, flags=re.MULTILINE)
        
        # Remover linhas vazias extras
        sanitized = re.sub(r'\n\s*\n\s*\n', '\n\n', sanitized)
        
        # Remover espaços em branco no início e fim
        sanitized = sanitized.strip()
        
        # Se a resposta estiver vazia após sanitização, retornar fallback
        if not sanitized:
            return "<!-- Conteúdo gerado pela IA -->"
        
        return sanitized

    def _generate_complete_html(self, context: Dict[str, Any]) -> str:
        """Gera HTML completo usando IA com validações."""
        try:
            # Preparar contexto melhorado
            enhanced_context = self._enhance_context_for_html(context)
            
            response = self._call_flow_api_for_html_generation(enhanced_context)

            return self._sanitize_ai_response(response)
        except Exception as e:
            logger.error(f"❌ Erro na geração de HTML: {e}")
            return self._generate_fallback_html(context)

    def _enhance_context_for_html(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Melhora contexto para geração de HTML consistente."""
        enhanced = context.copy()
        
        enhanced['html_rules'] = {
            'event_binding': 'Use (click)="metodo()" SEM prefixo "on"',
            'directives': 'Para *ngFor, *ngIf: certifique-se que CommonModule está importado',
            'consistency': 'Métodos no template devem existir na classe TypeScript',
            'preserve_styles': 'Preserve dimensões e layout do Figma usando classes CSS específicas'
        }
        
        # Adicionar informações sobre preservação de estilos
        if context.get('figma_css_styles'):
            enhanced['style_preservation'] = {
                'container_styles': self._extract_container_styles(context['figma_css_styles']),
                'component_styles': self._extract_component_styles(context['figma_css_styles'])
            }
        
        return enhanced

    def _extract_container_styles(self, figma_styles: Dict[str, Any]) -> Dict[str, Any]:
        """Extrai estilos do container principal."""
        container_styles = {}
        for node_id, style_data in figma_styles.items():
            if style_data.get('path') == '':  # Container principal
                container_styles = style_data.get('css', {})
                break
        return container_styles

    def _extract_component_styles(self, figma_styles: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """Extrai estilos dos componentes filhos."""
        component_styles = {}
        for node_id, style_data in figma_styles.items():
            path = style_data.get('path', '')
            if path and 'children[' in path:  # Componente filho
                component_styles[node_id] = {
                    'name': style_data.get('name', ''),
                    'css': style_data.get('css', {}),
                    'path': path
                }
        return component_styles



    def _generate_complete_typescript(self, context: Dict[str, Any]) -> str:
        """Gera TypeScript completo usando IA com validações."""
        try:
            # Preparar prompt melhorado
            enhanced_context = self._enhance_context_for_typescript(context)
            
            # Gerar código
            response = self._call_flow_api_for_typescript_generation(enhanced_context)

            return self._sanitize_ai_response(response)
        except Exception as e:
            logger.error(f"❌ Erro na geração de TypeScript: {e}")
            return self._generate_fallback_typescript(context)

    def _enhance_context_for_typescript(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Melhora o contexto com regras específicas para evitar erros."""
        enhanced = context.copy()
        
        # Adicionar regras específicas
        enhanced['angular_rules'] = {
            'imports_required': [
                'CommonModule para *ngFor, *ngIf',
                'FormsModule para [(ngModel)]',
                'ReactiveFormsModule para FormControl'
            ],
            'method_naming': 'Métodos devem ter nomes SEM prefixo "on" (buscar, limpar, não onBuscar, onLimpar)',
            'type_safety': 'Use apenas tipos válidos das interfaces definidas',
            'standalone': True
        }
        
        # Adicionar informações sobre estilos se disponível
        if context.get('figma_css_styles'):
            enhanced['style_info'] = {
                'has_custom_styles': True,
                'container_dimensions': self._extract_container_styles(context['figma_css_styles']),
                'component_count': len(self._extract_component_styles(context['figma_css_styles']))
            }
        
        return enhanced

    def _generate_complete_scss(self, context: Dict[str, Any]) -> str:
        """Gera SCSS completo do componente Angular."""
        if not self.flow_client:
            logger.error("❌ Flow API não disponível para geração de SCSS.")
            return self._generate_fallback_scss(context)
        
        logger.info(f"Gerando SCSS para: {context['component_name']}")
        logger.debug(f"   Componentes mapeados: {context['total_components']}")
        logger.debug(f"   Dados processados pela IA: {len(context.get('ai_processed_data', {}))}")
        
        # Construir system prompt
        system_prompt = self.prompts['system_prompts']['scss_generation']['role'] + "\n" + \
                       self.prompts['system_prompts']['scss_generation']['mission'] + "\n" + \
                       self.prompts['system_prompts']['scss_generation']['guidelines']
        
        # Construir user prompt
        user_prompt = self.prompts['user_prompts']['scss_generation']['task']
        user_prompt += "\n\nGere o SCSS completo do componente Angular '" + context['component_name'] + "' usando os dados pré-processados pela IA."
        
        # Adicionar estilos CSS do Figma para preservar dimensões e layout
        if context.get('figma_css_styles'):
            user_prompt += "\n\nESTILOS CSS DO FIGMA (PRESERVAR):"
            figma_styles = context['figma_css_styles']
            for node_id, style_data in figma_styles.items():
                css = style_data.get('css', {})
                if css:
                    user_prompt += f"\n- Node {node_id} ({style_data.get('name', '')}):"
                    for prop, value in css.items():
                        user_prompt += f"\n  {prop}: {value}"
        
        # Adicionar informações específicas sobre estrutura
        if context.get('structure_info', {}).get('has_header'):
            user_prompt += "\n\nIMPORTANTE: Este modal TEM um header com título. INCLUA o header no início do modal seguindo o template obrigatório."
            
            # Adicionar conteúdo específico do header se disponível
            if context.get('header_content'):
                header_content = context['header_content']
                user_prompt += "\n\nCONTEÚDO DO HEADER:"
                user_prompt += f"\n- Nome: {header_content.get('name', '')}"
                user_prompt += f"\n- HTML: {header_content.get('html', '')}"
                user_prompt += f"\n- Props: {header_content.get('props', {})}"
        
        if context.get('structure_info', {}).get('layout_type') == 'modal_with_header':
            user_prompt += "\n\nESTRUTURA OBRIGATÓRIA: header -> conteúdo -> actions"
        
        # Adicionar regras específicas para preservação de estilos
        user_prompt += "\n\nREGRAS PARA PRESERVAÇÃO DE ESTILOS:"
        user_prompt += "\n1. Use as dimensões exatas do Figma (width, height)"
        user_prompt += "\n2. Preserve o layout (display, flex-direction, gap)"
        user_prompt += "\n3. Mantenha alinhamentos (align-items, justify-content)"
        user_prompt += "\n4. Aplique cores e bordas conforme especificado"
        user_prompt += "\n5. Use classes do Design System + estilos customizados quando necessário"
        
        # Log do contexto sendo enviado
        context_json = json.dumps(context, ensure_ascii=False, indent=2)
        # Count tokens using tiktoken library
        encoding = tiktoken.get_encoding("cl100k_base")
        token_count = len(encoding.encode(context_json))
        logger.debug(f"   Enviando contexto para IA ({token_count} tokens)")
        
        user_prompt += "\n\n" + context_json
        
        try:
            model = self.ai_config['model']['generation']
            temperature = self.ai_config.get('temperature')
            max_tokens = self.ai_config.get('max_tokens')
            response = self.flow_client.with_model(model).get_answer(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            # Log detalhado da resposta
            logger.debug(f"   Resposta da IA recebida: {len(response)} caracteres")
            logger.debug(f"   Primeiros 200 chars: {response[:200]}...")
            logger.debug(f"   Últimos 200 chars: {response[-200:] if len(response) > 200 else response}")
            
            sanitized_response = self._sanitize_ai_response(response)
            logger.debug(f"   Resposta sanitizada: {len(sanitized_response)} caracteres")
            
            return sanitized_response
        except Exception as e:
            logger.error(f"❌ Erro na geração de SCSS: {e}")
            return self._generate_fallback_scss(context)

    def _call_flow_api_for_html_generation(self, context: Dict[str, Any]) -> str:
        """Chama Flow API para geração de HTML."""
        if not self.flow_client:
            raise Exception("Flow API não disponível")

        # Construir system prompt
        system_prompt = self.prompts['system_prompts']['html_generation']['role'] + "\n" + \
                       self.prompts['system_prompts']['html_generation']['mission'] + "\n" + \
                       self.prompts['system_prompts']['html_generation']['guidelines']

        # Construir user prompt
        user_prompt = self.prompts['user_prompts']['html_generation']['task']
        user_prompt += f"\n\nGere o HTML completo do componente Angular '{context['component_name']}' usando os dados pré-processados pela IA."

        # Adicionar informações sobre preservação de estilos
        if context.get('figma_css_styles'):
            user_prompt += "\n\nIMPORTANTE: Preserve as dimensões e layout do Figma:"
            figma_styles = context['figma_css_styles']
            for node_id, style_data in figma_styles.items():
                css = style_data.get('css', {})
                if css:
                    user_prompt += f"\n- {style_data.get('name', '')}:"
                    for prop, value in css.items():
                        if prop in ['width', 'height', 'gap', 'display', 'flexDirection']:
                            user_prompt += f"\n  {prop}: {value}"



        # Adicionar contexto como JSON
        context_json = json.dumps(context, ensure_ascii=False, indent=2)
        user_prompt += "\n\n" + context_json

        try:
            model = self.ai_config['model']['generation']
            temperature = self.ai_config.get('temperature')
            max_tokens = self.ai_config.get('max_tokens')
            response = self.flow_client.with_model(model).get_answer(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                temperature=temperature,
                max_tokens=max_tokens
            )
            return response
        except Exception as e:
            logger.error(f"❌ Erro na chamada Flow API para HTML: {e}")
            raise

    def _call_flow_api_for_typescript_generation(self, context: Dict[str, Any]) -> str:
        """Chama Flow API para geração de TypeScript."""
        if not self.flow_client:
            raise Exception("Flow API não disponível")

        # Construir system prompt
        system_prompt = self.prompts['system_prompts']['typescript_generation']['role'] + "\n" + \
                       self.prompts['system_prompts']['typescript_generation']['mission'] + "\n" + \
                       self.prompts['system_prompts']['typescript_generation']['guidelines']

        # Construir user prompt
        user_prompt = self.prompts['user_prompts']['typescript_generation']['task']
        user_prompt += f"\n\nGere o TypeScript completo do componente Angular '{context['component_name']}' usando os dados pré-processados pela IA."

        # Adicionar contexto como JSON
        context_json = json.dumps(context, ensure_ascii=False, indent=2)
        user_prompt += "\n\n" + context_json

        try:
            model = self.ai_config['model']['generation']
            temperature = self.ai_config.get('temperature')
            max_tokens = self.ai_config.get('max_tokens')
            response = self.flow_client.with_model(model).get_answer(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                temperature=temperature,
                max_tokens=max_tokens
            )
            return response
        except Exception as e:
            logger.error(f"❌ Erro na chamada Flow API para TypeScript: {e}")
            raise

    def _generate_fallback_html(self, context: Dict[str, Any]) -> str:
        """Gera HTML de fallback quando IA não está disponível."""
        component_name = context['component_name']
        html = f"""<!-- Componente Angular: {component_name} -->
<div class="{component_name}-container">
  <!-- ERRO NA GERAÇÃO, fallback criado -->
  <!-- TODO: Implementar estrutura HTML baseada nos componentes mapeados -->
  <p>Componente {component_name} - Estrutura HTML a ser implementada</p>
</div>"""
        return html

    def _generate_fallback_typescript(self, context: Dict[str, Any]) -> str:
        """Gera TypeScript de fallback quando IA não está disponível."""
        component_name = context['component_name']
        normalized_name = component_name.replace(' ', '-').lower()
        class_name = ''.join(word.capitalize() for word in normalized_name.split('-')) + 'Component'
        
        ts = f"""import {{ Component, Input, Output, EventEmitter }} from '@angular/core';

@Component({{
  selector: 'app-{normalized_name}',
  templateUrl: './{normalized_name}.component.html',
  styleUrls: ['./{normalized_name}.component.scss']
}})
export class {class_name} {{
  // ERRO NA GERAÇÃO, fallback criado
  // TODO: Implementar propriedades e métodos baseados nos componentes mapeados
}}"""
        return ts

    def _generate_fallback_scss(self, context: Dict[str, Any]) -> str:
        """Gera SCSS de fallback quando IA não está disponível."""
        component_name = context['component_name']
        scss = f""".{component_name}-container {{
  // ERRO NA GERAÇÃO, fallback criado
  // TODO: Implementar estilos baseados nos componentes mapeados
}}"""
        return scss

    def _load_ds_template(self, file_path: str) -> str:
        try:
            if not file_path:
                logger.debug("   ⚠️ Caminho de template vazio")
                return ""
            
            # Verificar se arquivo existe
            if not Path(file_path).exists():
                logger.warning(f"   Arquivo não encontrado: {file_path}")
                return ""
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                logger.debug(f"   Carregado: {file_path} ({len(content)} chars)")
                return content
        except Exception as e:
            logger.warning(f"   Erro ao carregar template do Design System: {e}")
            return ""
    
    def _map_figma_colors_to_css_classes(self, figma_data: FigmaComponentData) -> Dict[str, str]:
        """
        Mapeia cores do Figma para classes CSS do Design System.
        
        Args:
            figma_data: Dados do Figma
            
        Returns:
            Dict com mapeamento de cores para classes CSS
        """
        try:
            # Carregar dados do Design System para cores
            design_system_data = self._load_design_system_colors()
            
            if not design_system_data:
                logger.warning("⚠️ Dados do Design System não encontrados para mapeamento de cores")
                return {}
            
            # Criar mapeador de cores
            color_mapper = create_color_mapper_from_design_system(design_system_data)
            
            # Converter dados do Figma para formato esperado
            figma_dict = {
                'id': figma_data.figma_id,
                'name': figma_data.component_name,
                'css': figma_data.css_styles,
                'children': []
            }
            
            # Adicionar children se disponíveis
            if figma_data.metadata.get('children'):
                figma_dict['children'] = figma_data.metadata.get('children', [])
            
            logger.debug(f"🎨 Dados do Figma para mapeamento de cores: {figma_dict}")
            
            # Mapear cores
            color_mapping = color_mapper.map_figma_colors_to_css_classes(figma_dict)
            
            logger.info(f"🎨 Mapeadas {len(color_mapping)} cores do Figma para classes CSS")
            return color_mapping
            
        except Exception as e:
            logger.error(f"❌ Erro no mapeamento de cores: {e}")
            return {}
    
    def _load_design_system_colors(self) -> Dict[str, Any]:
        """Carrega dados de cores do Design System."""
        return load_design_system_colors()
    
    def _extract_structure_info(self, figma_data: FigmaComponentData) -> Dict[str, Any]:
        """
        Extrai informações de estrutura do Figma analisando elementos HTML.
        
        Args:
            figma_data: Dados do Figma
            
        Returns:
            Dict com informações de estrutura
        """
        return extract_structure_info(figma_data.__dict__)

    def _save_component_files(self, component_name: str, html: str, ts: str, scss: str, output_dir: str):
        """Salva arquivos do componente Angular."""
        # Salvar arquivos usando utilitário Angular
        component_dir = generate_angular_component_files(component_name, html, ts, scss, output_dir)

        # Log de confirmação
        logger.info(f"💾 Componente Angular salvo em {component_dir}")
        logger.debug(f"   HTML: {len(html)} caracteres")
        logger.debug(f"   TypeScript: {len(ts)} caracteres")
        logger.debug(f"   SCSS: {len(scss)} caracteres")
