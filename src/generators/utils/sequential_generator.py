# -*- coding: utf-8 -*-
"""
Gerador sequencial que garante contexto entre etapas HTML → TypeScript → SCSS.
Cada etapa recebe o resultado da anterior para manter consistência.
"""

import re
from typing import Dict, Any, <PERSON>ple
from pathlib import Path

from src.utils.logging import get_logger
from .element_order_validator import ElementOrderValidator

logger = get_logger(__name__)


class SequentialGenerator:
    """
    Gera código Angular em sequência, passando contexto entre etapas.
    """
    
    def __init__(self, flow_client, prompts: Dict[str, Any], ai_config: Dict[str, Any]):
        self.flow_client = flow_client
        self.prompts = prompts
        self.ai_config = ai_config
        self.order_validator = ElementOrderValidator()
    
    def generate_with_context_flow(self, base_context: Dict[str, Any]) -> Tuple[str, str, str]:
        """
        Gera HTML, TypeScript e SCSS em sequência com contexto compartilhado.
        
        Args:
            base_context: Contexto base com dados do Figma e mapeamento
            
        Returns:
            Tuple[html, typescript, scss]
        """
        logger.info("🔄 Iniciando geração sequencial com contexto...")

        # ETAPA 0: Aplicar validações de ordem e duplicatas
        logger.info("🔍 Validando ordem e duplicatas...")
        validated_context = self._apply_order_validations(base_context)

        # ETAPA 1: Gerar HTML
        logger.info("📝 Etapa 1/3: Gerando HTML...")
        html = self._generate_html(validated_context)
        
        # ETAPA 2: Gerar TypeScript com contexto do HTML
        logger.info("📝 Etapa 2/3: Gerando TypeScript com contexto do HTML...")
        ts_context = self._build_typescript_context(base_context, html)
        typescript = self._generate_typescript(ts_context)
        
        # ETAPA 3: Gerar SCSS com contexto HTML + TypeScript
        logger.info("📝 Etapa 3/3: Gerando SCSS com contexto completo...")
        scss_context = self._build_scss_context(base_context, html, typescript)
        scss = self._generate_scss(scss_context)
        
        logger.info("✅ Geração sequencial concluída")
        return html, typescript, scss
    
    def _generate_html(self, context: Dict[str, Any]) -> str:
        """Gera HTML usando prompt específico."""
        try:
            prompt = self.prompts['generation']['html']['system_prompt']
            user_prompt = self.prompts['generation']['html']['user_prompt']
            
            # Preparar contexto específico para HTML
            html_context = self._prepare_html_context(context)
            
            model = self.ai_config['model']['analysis']
            temperature = self.ai_config.get('temperature')
            max_tokens = self.ai_config.get('max_tokens')
            # Formatar o user_prompt de forma segura
            try:
                formatted_user_prompt = user_prompt.format(**html_context)
            except (KeyError, ValueError) as e:
                logger.warning(f"⚠️ Erro na formatação do prompt HTML: {e}")
                formatted_user_prompt = user_prompt
            
            response = self.flow_client.with_model(model).get_answer(
                system_prompt=prompt,
                user_prompt=formatted_user_prompt,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            return self._sanitize_ai_response(response)
            
        except Exception as e:
            logger.error(f"❌ Erro na geração de HTML: {e}")
            return self._generate_fallback_html(context)
    
    def _generate_typescript(self, context: Dict[str, Any]) -> str:
        """Gera TypeScript usando prompt específico com contexto do HTML."""
        try:
            prompt = self.prompts['generation']['typescript']['system_prompt']
            user_prompt = self.prompts['generation']['typescript']['user_prompt']
            
            model = self.ai_config['model']['analysis']
            temperature = self.ai_config.get('temperature')
            max_tokens = self.ai_config.get('max_tokens')
            
            # Formatar o user_prompt de forma segura
            try:
                formatted_user_prompt = user_prompt.format(**context)
            except (KeyError, ValueError) as e:
                logger.warning(f"⚠️ Erro na formatação do prompt TypeScript: {e}")
                formatted_user_prompt = user_prompt
            
            response = self.flow_client.with_model(model).get_answer(
                system_prompt=prompt,
                user_prompt=formatted_user_prompt,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            return self._sanitize_ai_response(response)
            
        except Exception as e:
            logger.error(f"❌ Erro na geração de TypeScript: {e}")
            return self._generate_fallback_typescript(context)
    
    def _generate_scss(self, context: Dict[str, Any]) -> str:
        """Gera SCSS usando prompt específico com contexto HTML + TypeScript."""
        try:
            prompt = self.prompts['generation']['scss']['system_prompt']
            user_prompt = self.prompts['generation']['scss']['user_prompt']

            # Debug: Log do contexto
            component_name = context.get('component_name', 'unknown')

            model = self.ai_config['model']['analysis']
            temperature = self.ai_config.get('temperature')
            max_tokens = self.ai_config.get('max_tokens')
            
            # Formatar o user_prompt de forma segura
            try:
                formatted_user_prompt = user_prompt.format(**context)
            except (KeyError, ValueError) as e:
                logger.warning(f"⚠️ Erro na formatação do prompt SCSS: {e}")
                formatted_user_prompt = user_prompt
            
            response = self.flow_client.with_model(model).get_answer(
                system_prompt=prompt,
                user_prompt=formatted_user_prompt,
                temperature=temperature,
                max_tokens=max_tokens
            )

            # Debug: Log da resposta
            logger.debug(f"🔍 Resposta SCSS para {component_name}: {response[:100]}...")

            sanitized = self._sanitize_ai_response(response)
            logger.debug(f"🔍 SCSS sanitizado para {component_name}: {sanitized[:100]}...")

            return sanitized

        except Exception as e:
            logger.error(f"❌ Erro na geração de SCSS: {e}")
            return self._generate_fallback_scss(context)
    
    def _build_typescript_context(self, base_context: Dict[str, Any], html: str) -> Dict[str, Any]:
        """Constrói contexto para TypeScript incluindo análise do HTML."""
        ts_context = base_context.copy()
        
        # Analisar HTML para extrair informações necessárias para TypeScript
        html_analysis = self._analyze_html_for_typescript(html)
        
        ts_context.update({
            'generated_html': html,
            'html_analysis': html_analysis,
            'required_methods': html_analysis.get('methods', []),
            'required_properties': html_analysis.get('properties', []),
            'required_imports': html_analysis.get('imports', [])
        })
        
        return ts_context
    
    def _build_scss_context(self, base_context: Dict[str, Any], html: str, typescript: str) -> Dict[str, Any]:
        """Constrói contexto para SCSS incluindo análise do HTML e TypeScript."""
        scss_context = base_context.copy()
        
        # Analisar HTML e TypeScript para SCSS
        html_analysis = self._analyze_html_for_scss(html)
        ts_analysis = self._analyze_typescript_for_scss(typescript)
        
        scss_context.update({
            'generated_html': html,
            'generated_typescript': typescript,
            'html_analysis': html_analysis,
            'ts_analysis': ts_analysis,
            'css_classes_used': html_analysis.get('classes', []),
            'component_structure': html_analysis.get('structure', {}),
            'design_system_classes': html_analysis.get('design_system_classes', [])
        })
        
        return scss_context
    
    def _prepare_html_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Prepara contexto específico para geração de HTML."""
        html_context = context.copy()
        
        # Extrair templates do design system mapeados
        design_system_templates = []
        for mapped_comp in context.get('mapped_components', []):
            if 'design_system' in mapped_comp and 'template' in mapped_comp['design_system']:
                design_system_templates.append({
                    'name': mapped_comp['design_system']['name'],
                    'template': mapped_comp['design_system']['template'],
                    'confidence': mapped_comp.get('confidence', 0)
                })
        
        html_context['design_system_templates'] = design_system_templates
        html_context['component_type'] = context.get('component_type', 'component')

        # Adicionar ordem dos botões se disponível (será usado nas regras críticas)
        if context.get('button_order'):
            html_context['button_order'] = context['button_order']
            logger.info(f"📋 Ordem dos botões será aplicada: {context['button_order']}")

        return html_context
    
    def _analyze_html_for_typescript(self, html: str) -> Dict[str, Any]:
        """Analisa HTML para extrair informações necessárias para TypeScript."""
        analysis = {
            'methods': [],
            'properties': [],
            'imports': [],
            'directives_used': []
        }
        
        # Extrair métodos de eventos
        method_matches = re.findall(r'\(click\)="(\w+)\(\)"', html)
        analysis['methods'].extend(method_matches)
        
        # Extrair propriedades de ngModel
        property_matches = re.findall(r'\[\(ngModel\)\]="(\w+)"', html)
        analysis['properties'].extend(property_matches)
        
        # Detectar diretivas Angular
        if '*ngFor' in html or '*ngIf' in html:
            analysis['imports'].append('CommonModule')
            analysis['directives_used'].append('structural')
        
        if '[(ngModel)]' in html:
            analysis['imports'].append('FormsModule')
            analysis['directives_used'].append('forms')
        
        return analysis
    
    def _analyze_html_for_scss(self, html: str) -> Dict[str, Any]:
        """Analisa HTML para extrair informações necessárias para SCSS."""
        analysis = {
            'classes': [],
            'ids': [],
            'design_system_classes': [],
            'structure': {}
        }
        
        # Extrair classes CSS
        class_matches = re.findall(r'class="([^"]*)"', html)
        for match in class_matches:
            classes = match.split()
            analysis['classes'].extend(classes)
            
            # Identificar classes do design system (brad-*)
            ds_classes = [cls for cls in classes if cls.startswith('brad-')]
            analysis['design_system_classes'].extend(ds_classes)
        
        # Extrair IDs
        id_matches = re.findall(r'id="([^"]*)"', html)
        analysis['ids'].extend(id_matches)
        
        # Analisar estrutura básica
        analysis['structure'] = {
            'has_form': '<form' in html,
            'has_table': 'brad-table' in html,
            'has_modal': 'brad-modal' in html or 'modal' in html.lower(),
            'has_buttons': 'brad-button' in html or '<button' in html
        }
        
        return analysis
    
    def _analyze_typescript_for_scss(self, typescript: str) -> Dict[str, Any]:
        """Analisa TypeScript para extrair informações úteis para SCSS."""
        analysis = {
            'component_name': '',
            'has_inputs': False,
            'has_outputs': False,
            'methods': []
        }
        
        # Extrair nome do componente
        component_match = re.search(r'export class (\w+)Component', typescript)
        if component_match:
            analysis['component_name'] = component_match.group(1)
        
        # Detectar @Input e @Output
        analysis['has_inputs'] = '@Input' in typescript
        analysis['has_outputs'] = '@Output' in typescript
        
        # Extrair métodos
        method_matches = re.findall(r'(\w+)\(\)\s*{', typescript)
        analysis['methods'] = method_matches
        
        return analysis
    
    def _sanitize_ai_response(self, response: str) -> str:
        """Sanitiza resposta da IA removendo blocos de código desnecessários."""
        if not response:
            return ""
        
        sanitized = response
        
        # Remover marcadores de código
        sanitized = re.sub(r'^```(?:typescript|html|scss|css|javascript|js|ts|angular|component)\s*$', '', sanitized, flags=re.MULTILINE)
        sanitized = re.sub(r'^\s*```\s*$', '', sanitized, flags=re.MULTILINE)
        
        # Remover linhas vazias extras
        sanitized = re.sub(r'\n\s*\n\s*\n', '\n\n', sanitized)
        sanitized = sanitized.strip()
        
        return sanitized if sanitized else "<!-- Conteúdo gerado pela IA -->"
    
    def _generate_fallback_html(self, context: Dict[str, Any]) -> str:
        """Gera HTML de fallback em caso de erro."""
        component_name = context.get('component_name', 'component')
        return f'<div class="brad-container">\n  <!-- {component_name} -->\n  <p>Componente {component_name}</p>\n</div>'
    
    def _generate_fallback_typescript(self, context: Dict[str, Any]) -> str:
        """Gera TypeScript de fallback em caso de erro."""
        component_name = context.get('component_name', 'Component')
        normalized_name = component_name.lower().replace(' ', '-')
        
        return f"""import {{ Component }} from '@angular/core';

@Component({{
  selector: 'app-{normalized_name}',
  templateUrl: './{normalized_name}.component.html',
  styleUrls: ['./{normalized_name}.component.scss']
}})
export class {component_name.replace(' ', '')}Component {{
  constructor() {{}}
}}"""
    
    def _generate_fallback_scss(self, context: Dict[str, Any]) -> str:
        """Gera SCSS de fallback em caso de erro."""
        return "// Estilos do componente\n.brad-container {\n  // Estilos básicos\n}"

    def _apply_order_validations(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Aplica validações de ordem e duplicatas no contexto."""
        try:
            validated_context = context.copy()

            # Validar ordem dos elementos se houver dados processados
            if 'ai_processed_data' in context:
                ai_data = context['ai_processed_data']

                # Validar ordem dos elementos de texto
                if 'text_elements' in ai_data:
                    text_elements = ai_data['text_elements']
                    if text_elements and 'figma_data' in context:
                        ordered_texts = self.order_validator.validate_element_order(
                            context['figma_data'], text_elements
                        )
                        validated_context['ai_processed_data']['text_elements'] = ordered_texts

                # Extrair ordem correta dos botões do Figma
                if 'figma_data' in context:
                    button_order = self.order_validator.extract_button_order_from_figma(
                        context['figma_data']
                    )
                    if button_order:
                        validated_context['button_order'] = button_order
                        logger.info(f"📋 Ordem dos botões definida: {button_order}")

            return validated_context

        except Exception as e:
            logger.error(f"❌ Erro ao aplicar validações de ordem: {e}")
            return context
