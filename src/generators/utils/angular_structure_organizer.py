"""
Utilitário para reorganizar estrutura de arquivos Angular gerados.

Este módulo contém funções específicas para organizar a estrutura de componentes Angular
de forma que facilite a integração com projetos existentes.
"""

import json
import glob
import shutil
from pathlib import Path
from typing import Dict, Any
from src.utils.logging import get_logger

logger = get_logger(__name__)


class AngularStructureOrganizer:
    """
    Organizador de estrutura de arquivos Angular.
    
    Reorganiza componentes gerados para facilitar integração com projetos existentes.
    """
    
    def reorganize_angular_structure(self, angular_path: Path):
        """
        Reorganiza a estrutura Angular para facilitar integração com projetos existentes.
        
        Estrutura final:
        angular/
        └── [component_name]/
            ├── [component_name].component.html
            ├── [component_name].component.ts
            ├── [component_name].component.scss
            └── components/
                ├── [child1]/
                ├── [child2]/
                └── ...
        
        Args:
            angular_path: Caminho para a pasta angular
        """
        logger.info("🔄 Reorganizando estrutura Angular...")
        
        # Encontrar o wrapper correto baseado nos metadados salvos
        wrapper_dir = self._find_wrapper_directory(angular_path)
        
        if not wrapper_dir:
            logger.warning("⚠️ Wrapper não encontrado, mantendo estrutura atual")
            return
        
        # Encontrar todos os componentes filhos (exceto o wrapper)
        child_components = []
        for item in angular_path.iterdir():
            if (item.is_dir() and 
                not item.name.endswith('_metadata') and 
                item != wrapper_dir and
                any(item.glob("*.component.*"))):
                child_components.append(item)
        
        if not child_components:
            logger.info("ℹ️ Nenhum componente filho encontrado para reorganizar")
            return
        
        # Criar pasta components dentro do wrapper
        components_dir = wrapper_dir / "components"
        components_dir.mkdir(exist_ok=True)
        
        # Mover componentes filhos para pasta components
        for child_dir in child_components:
            # Mover pasta do componente filho para components/
            new_path = components_dir / child_dir.name
            if new_path.exists():
                # Se já existe, remover primeiro
                shutil.rmtree(new_path)

            # Usar copytree em vez de rename para evitar problemas de corrupção
            shutil.copytree(child_dir, new_path)
            # Remover diretório original
            shutil.rmtree(child_dir)
            logger.debug(f"📦 Movido {child_dir.name} para components/")
        
        # Atualizar imports no wrapper TypeScript
        self._update_wrapper_imports(wrapper_dir, components_dir)
        
        logger.info(f"✅ Estrutura reorganizada: {wrapper_dir.name}/components/")
    
    def _find_wrapper_directory(self, angular_path: Path) -> Path | None:
        """
        Encontra o diretório do wrapper correto baseado nos metadados salvos.
        
        Args:
            angular_path: Caminho para a pasta angular
            
        Returns:
            Path do diretório do wrapper ou None se não encontrado
        """
        # Procurar por arquivos de metadados de wrapper na pasta figma_processed
        figma_processed_dir = str(angular_path).replace('/angular/', '/figma_processed/')
        metadata_pattern = f"{figma_processed_dir}/**/*_wrapper_metadata.json"
        metadata_files = glob.glob(metadata_pattern, recursive=True)
        
        if not metadata_files:
            logger.debug("🔍 Nenhum arquivo de metadados de wrapper encontrado, usando lógica de fallback")
            return self._find_wrapper_directory_fallback(angular_path)
        
        # Carregar metadados do wrapper para obter o nome normalizado
        wrapper_names = []
        for metadata_file in metadata_files:
            try:
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                    wrapper_names.append(metadata['normalized_name'])
                    logger.debug(f"📋 Wrapper encontrado nos metadados: {metadata['normalized_name']}")
            except Exception as e:
                logger.warning(f"⚠️ Erro ao ler metadados {metadata_file}: {e}")
        
        # Procurar pelo diretório do wrapper na pasta angular
        for item in angular_path.iterdir():
            if (item.is_dir() and 
                not item.name.endswith('_metadata') and
                item.name in wrapper_names and
                any(item.glob("*.component.*"))):
                logger.info(f"🎯 Wrapper identificado: {item.name}")
                return item
        
        logger.warning("⚠️ Wrapper não encontrado pelos metadados, usando lógica de fallback")
        return self._find_wrapper_directory_fallback(angular_path)
    
    def _find_wrapper_directory_fallback(self, angular_path: Path) -> Path | None:
        """
        Lógica de fallback para encontrar o wrapper quando metadados não estão disponíveis.
        Assume que o wrapper é o componente com mais referências a outros componentes.
        
        Args:
            angular_path: Caminho para a pasta angular
            
        Returns:
            Path do diretório do wrapper ou None se não encontrado
        """
        component_dirs = []
        
        # Coletar todos os diretórios de componentes
        for item in angular_path.iterdir():
            if (item.is_dir() and 
                not item.name.endswith('_metadata') and
                any(item.glob("*.component.*"))):
                component_dirs.append(item)
        
        if not component_dirs:
            return None
        
        if len(component_dirs) == 1:
            return component_dirs[0]
        
        # Analisar qual componente tem mais imports de outros componentes (provável wrapper)
        wrapper_candidate = None
        max_imports = 0
        
        for comp_dir in component_dirs:
            ts_file = next(comp_dir.glob("*.component.ts"), None)
            if ts_file:
                try:
                    with open(ts_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        # Contar imports de outros componentes
                        import_count = content.count('import {') + content.count('import{')
                        if import_count > max_imports:
                            max_imports = import_count
                            wrapper_candidate = comp_dir
                except Exception as e:
                    logger.debug(f"Erro ao analisar {ts_file}: {e}")
        
        if wrapper_candidate:
            logger.info(f"🎯 Wrapper identificado por fallback: {wrapper_candidate.name}")
        
        return wrapper_candidate or component_dirs[0]  # Se tudo falhar, pega o primeiro
    
    def _update_wrapper_imports(self, wrapper_dir: Path, components_dir: Path):
        """
        Atualiza imports no arquivo TypeScript do wrapper.
        
        Args:
            wrapper_dir: Diretório do wrapper
            components_dir: Diretório dos componentes filhos
        """
        ts_file = next(wrapper_dir.glob("*.component.ts"), None)
        if not ts_file:
            return
        
        # Ler conteúdo atual
        with open(ts_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Atualizar imports para usar caminho relativo components/
        updated_content = content
        for child_dir in components_dir.iterdir():
            if child_dir.is_dir():
                child_name = child_dir.name
                old_import = f"import {{ {self._to_pascal_case(child_name)}Component }} from '../{child_name}/{child_name}.component';"
                new_import = f"import {{ {self._to_pascal_case(child_name)}Component }} from './components/{child_name}/{child_name}.component';"
                updated_content = updated_content.replace(old_import, new_import)
        
        # Salvar arquivo atualizado
        with open(ts_file, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        logger.debug(f"📝 Imports atualizados em {ts_file.name}")
    
    def _to_pascal_case(self, name: str) -> str:
        """
        Converte nome para PascalCase.
        
        Args:
            name: Nome em kebab-case ou snake_case
            
        Returns:
            Nome em PascalCase
        """
        # Substituir hífens e underscores por espaços, depois capitalizar cada palavra
        words = name.replace('-', ' ').replace('_', ' ').split()
        return ''.join(word.capitalize() for word in words)


def reorganize_angular_structure(angular_path: Path):
    """
    Função de conveniência para reorganizar estrutura Angular.
    
    Args:
        angular_path: Caminho para a pasta angular
    """
    organizer = AngularStructureOrganizer()
    organizer.reorganize_angular_structure(angular_path)
