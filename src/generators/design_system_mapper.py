"""
Design System Mapper - Mapeamento usando BM25 + IA como fallback.
Implementa busca BM25 para casos simples e IA para casos complexos.
"""

import json
import re
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass

from src.utils.config import Config<PERSON>oa<PERSON>
from src.utils.logging import get_logger
from src.generators.figma_reader import FigmaComponentData

logger = get_logger(__name__)

class ComponentNotFoundError(Exception):
    """Exceção lançada quando um componente não é encontrado no Design System."""
    
    def __init__(self, component_name: str, search_methods_used: List[str], available_components: List[str] = None):
        self.component_name = component_name
        self.search_methods_used = search_methods_used
        self.available_components = available_components or []
        
        message = f"Componente '{component_name}' não encontrado no Design System após tentar: {', '.join(search_methods_used)}"
        super().__init__(message)

@dataclass
class DesignSystemComponent:
    """Componente do Design System."""
    component_id: str
    name: str
    category: str
    description: str
    properties: Dict[str, Any]
    variants: List[str]
    file_path: str
    markdown_content: str = ""

@dataclass
class ComponentMapping:
    """Mapeamento entre webcomponent do Figma e Design System."""
    figma_webcomponent: Dict[str, Any]
    design_system_component: Optional[DesignSystemComponent]
    confidence: float
    mapping_reason: str
    properties_mapping: Dict[str, Any]
    search_method: str = ""

@dataclass
class DesignSystemMapping:
    """Resultado do mapeamento com Design System."""
    component_name: str
    figma_id: str
    mappings: List[ComponentMapping]
    design_system_components: List[DesignSystemComponent]
    metadata: Dict[str, Any]

class BM25Index:
    """Índice BM25 para busca rápida em documentação Markdown."""
    
    def __init__(self):
        self.documents = []
        self.document_ids = []
        self.bm25 = None
        # Não construir índice no __init__, será construído quando documentos forem adicionados
    
    def _build_index(self):
        """Constrói índice BM25 a partir da documentação."""
        try:
            from rank_bm25 import BM25Okapi
            import numpy as np
            
            # Tokenizar documentos
            tokenized_docs = []
            for doc in self.documents:
                tokens = self._tokenize(doc['content'])
                tokenized_docs.append(tokens)
            
            if tokenized_docs:
                self.bm25 = BM25Okapi(tokenized_docs)
                logger.info(f"📚 Índice BM25 construído com {len(self.documents)} documentos")
            else:
                logger.warning("⚠️ Nenhum documento para construir índice BM25")
                
        except ImportError:
            logger.warning("⚠️ rank_bm25 não instalado. Usando busca simples.")
            self.bm25 = None
    
    def _tokenize(self, text: str) -> List[str]:
        """Tokeniza texto para busca BM25."""
        # Remover caracteres especiais e dividir em palavras
        text = re.sub(r'[^\w\s]', ' ', text.lower())
        tokens = text.split()
        return [token for token in tokens if len(token) > 2]  # Filtrar tokens muito pequenos
    
    def add_document(self, doc_id: str, content: str, metadata: Dict[str, Any] = None):
        """Adiciona documento ao índice."""
        self.documents.append({
            'id': doc_id,
            'content': content,
            'metadata': metadata or {}
        })
        self.document_ids.append(doc_id)
    
    def search(self, query: str, top_k: int = 5) -> List[Tuple[str, float]]:
        """Busca documentos usando BM25."""
        if not self.bm25 or not self.documents:
            return []
        
        try:
            # Tokenizar query
            query_tokens = self._tokenize(query)
            if not query_tokens:
                return []
            
            # Buscar com BM25
            scores = self.bm25.get_scores(query_tokens)
            
            # Ordenar por score
            doc_scores = list(zip(self.document_ids, scores))
            doc_scores.sort(key=lambda x: x[1], reverse=True)
            
            # Retornar top_k resultados
            return doc_scores[:top_k]
            
        except Exception as e:
            logger.error(f"❌ Erro na busca BM25: {e}")
            return []

class DesignSystemMapper:
    """
    Mapeador que usa BM25 para busca rápida e IA como fallback.
    """
    
    def __init__(self, config_path: str = "project_config.yaml", flow_client=None):
        self.config_loader = ConfigLoader(config_path)
        self.config = self.config_loader.load_config()
        self.ai_config = self.config_loader.get_ai_config()
        
        # Carregar prompts
        self.prompts = self._load_prompts()
        
        # Cache de componentes do Design System
        self.ds_components: Dict[str, DesignSystemComponent] = {}
        self.ds_index: Dict[str, List[str]] = {}
        
        # Índice BM25 para busca rápida
        self.bm25_index = BM25Index()
        
        # Flow API para IA (compartilhado)
        self.flow_client = flow_client
        
        # Carregar índice do Design System
        self._load_design_system_index()
    
    def _load_prompts(self) -> Dict[str, Any]:
        """Carrega prompts do arquivo YAML."""
        try:
            import yaml
            prompts_path = Path(__file__).parent / "prompts.yaml"
            if prompts_path.exists():
                with open(prompts_path, 'r', encoding='utf-8') as f:
                    prompts = yaml.safe_load(f)
                    logger.debug("📝 Prompts carregados para Design System Mapper")
                    return prompts
        except Exception as e:
            logger.warning(f"Erro ao carregar prompts: {e}")
        
        return {}
    

    
    def _load_design_system_index(self):
        """Carrega índice do Design System e constrói índice BM25."""
        # Obter configurações corretas do design system
        ds_config = self.config.get('design_system', {})
        storybook_slug = ds_config.get('slug', 'storybook')
        paths_config = self.config.get('output', {})

        # Caminho para o índice do Storybook
        storybook_index_name = "storybook_index.json"
        storybook_index_path = Path(paths_config.get('design_system_path', 'data/design_system')) / storybook_slug / storybook_index_name

        if storybook_index_path.exists():
            with open(storybook_index_path, 'r', encoding='utf-8') as f:
                index_data = json.load(f)
                self._process_design_system_index(index_data)
                logger.info(f"📚 Índice do Design System carregado: {len(self.ds_components)} componentes")
        else:
            logger.debug("Índice do Storybook não encontrado: verificar configuração do design system e pasta do storybook")
            print("\n⚠️  Índice do Storybook não encontrado!")
            print("⚠️   Confira o campo 'design_system > slug' no arquivo de configuração e se a pasta com mesmo nome existe no caminho especificado.")
            raise Exception("Arquivos do Design System não encontrados, não será possível continuar.")
    
    def _process_design_system_index(self, index_data: Dict[str, Any]):
        """Processa dados do índice do Design System e constrói índice BM25."""
        # Processar componentes por categoria
        categories = ['components', 'templates', 'services', 'classes', 'images']
        
        for category in categories:
            if category in index_data:
                category_data = index_data[category]
                if isinstance(category_data, dict):
                    for component_id, component_data in category_data.items():
                        component = self._create_ds_component(component_data, category, component_id)
                        if component:
                            self.ds_components[component.component_id] = component
                            
                            # Indexar por nome para busca
                            name_key = component.name.lower()
                            if name_key not in self.ds_index:
                                self.ds_index[name_key] = []
                            self.ds_index[name_key].append(component.component_id)
                            
                            # Adicionar ao índice BM25
                            self.bm25_index.add_document(
                                component_id,
                                f"{component.name} {component.description} {component.category}",
                                {
                                    'name': component.name,
                                    'category': component.category,
                                    'description': component.description
                                }
                            )
        
        # Reconstruir índice BM25 após adicionar todos os documentos
        self.bm25_index._build_index()
    
    def _create_ds_component(self, component_data: Dict[str, Any], category: str, component_id: str) -> Optional[DesignSystemComponent]:
        """Cria objeto DesignSystemComponent a partir dos dados."""
        try:
            name = component_data.get('name', '')
            description = component_data.get('description', '')
            
            if not name:
                return None
            
            # Extrair propriedades e variantes (se disponíveis)
            properties = component_data.get('properties', {})
            variants = component_data.get('variants', [])
            file_path = component_data.get('file', '')
            
            # Carregar conteúdo Markdown se disponível
            markdown_content = self._load_markdown_content(file_path)
            
            component = DesignSystemComponent(
                component_id=component_id,
                name=name,
                category=category,
                description=description,
                properties=properties,
                variants=variants,
                file_path=file_path,
                markdown_content=markdown_content
            )
            
            return component
            
        except Exception as e:
            logger.warning(f"Erro ao criar componente DS: {e}")
            return None
    
    def _load_markdown_content(self, file_path: str) -> str:
        """Carrega conteúdo Markdown do arquivo."""
        try:
            if not file_path:
                return ""
            
            # Construir caminho completo
            ds_config = self.config.get('design_system', {})
            storybook_slug = ds_config.get('slug', 'storybook')
            paths_config = self.config.get('output', {})
            
            full_path = Path(paths_config.get('design_system_path', 'data/design_system')) / storybook_slug / file_path
            
            if full_path.exists():
                with open(full_path, 'r', encoding='utf-8') as f:
                    return f.read()
            
        except Exception as e:
            logger.debug(f"Erro ao carregar Markdown: {e}")
        
        return ""
    

    
    def map_figma_to_design_system(self, figma_data: FigmaComponentData) -> DesignSystemMapping:
        """
        Mapeia webcomponents do Figma para componentes do Design System usando BM25 + IA.
        
        Args:
            figma_data: Dados do componente Figma
            
        Returns:
            DesignSystemMapping com mapeamentos encontrados
        """
        logger.info(f"🔗 Mapeando {len(figma_data.webcomponents)} webcomponents para Design System (BM25 + IA)...")
        
        mappings = []
        design_system_components = []
        errors = []
        
        for webcomponent in figma_data.webcomponents:
            try:
                mapping = self._map_single_webcomponent(webcomponent)
                mappings.append(mapping)
                
                if mapping.design_system_component:
                    design_system_components.append(mapping.design_system_component)
                    
            except ComponentNotFoundError as e:
                # Registrar erro detalhado
                error_info = {
                    'component_name': e.component_name,
                    'search_methods_used': e.search_methods_used,
                    'available_components_count': len(e.available_components),
                    'webcomponent_data': webcomponent
                }
                errors.append(error_info)
                
                logger.debug(f"❌ {e}")
                logger.debug(f"📋 Componentes disponíveis: {len(e.available_components)}")
                
                # Criar mapping vazio para manter consistência
                mappings.append(ComponentMapping(
                    figma_webcomponent=webcomponent,
                    design_system_component=None,
                    confidence=0.0,
                    mapping_reason=f"ERRO: {str(e)}",
                    properties_mapping={},
                    search_method="error"
                ))
        
        # Preparar metadados com informações de erro
        total_webcomponents = len(figma_data.webcomponents)
        mapped_components = len([m for m in mappings if m.design_system_component is not None])
        
        metadata = {
            'total_webcomponents': total_webcomponents,
            'mapped_components': mapped_components,
            'failed_components': len(errors),
            'average_confidence': sum(m.confidence for m in mappings if m.design_system_component) / len([m for m in mappings if m.design_system_component]) if [m for m in mappings if m.design_system_component] else 0,
            'mapping_method': 'bm25_plus_ai',
            'search_methods': {
                'bm25': len([m for m in mappings if m.search_method == 'bm25']),
                'ai': len([m for m in mappings if m.search_method == 'ai']),
                'exact': len([m for m in mappings if m.search_method == 'exact']),
                'pattern': len([m for m in mappings if m.search_method == 'pattern']),
                'error': len([m for m in mappings if m.search_method == 'error'])
            },
            'errors': errors
        }
        
        mapping_result = DesignSystemMapping(
            component_name=figma_data.component_name,
            figma_id=figma_data.figma_id,
            mappings=mappings,
            design_system_components=design_system_components,
            metadata=metadata
        )
        
        # Log detalhado dos resultados
        success_rate = (mapped_components / total_webcomponents) * 100 if total_webcomponents > 0 else 0
        logger.info(f"✅ Mapeamento concluído: {mapped_components}/{total_webcomponents} componentes mapeados ({success_rate:.1f}%)")
        logger.debug(f"📊 Métodos de busca: BM25={metadata['search_methods']['bm25']}, IA={metadata['search_methods']['ai']}, Exato={metadata['search_methods']['exact']}, Padrão={metadata['search_methods']['pattern']}")
        
        if errors:
            logger.debug(f"⚠️ {len(errors)} componentes falharam no mapeamento:")
            for error in errors:
                logger.debug(f"   - {error['component_name']} (tentou: {', '.join(error['search_methods_used'])})")
        
        return mapping_result
    
    def _map_single_webcomponent(self, webcomponent: Dict[str, Any]) -> ComponentMapping:
        """Mapeia um webcomponent específico para Design System usando BM25 + IA."""
        webcomponent_name = webcomponent.get('name', '').lower()
        component_id = webcomponent.get('componentId', '')
        webcomponent_type = webcomponent.get('type', 'confirmed')
        
        # Lista para rastrear métodos de busca utilizados
        search_methods_used = []
        
        # Para webcomponents "possible", usar IA diretamente
        if webcomponent_type == 'possible':
            logger.debug(f"🔍 Webcomponent 'possible' detectado: {webcomponent_name}")
            ds_component, confidence, reason, method = self._find_ai_match(webcomponent_name, webcomponent)
            search_methods_used.append("busca com IA (possible webcomponent)")
        else:
            # Para webcomponents confirmados, usar fluxo normal
            # 1. Tentar correspondência exata (mais rápida)
            ds_component, confidence, reason, method = self._find_exact_match(webcomponent_name, component_id)
            search_methods_used.append("correspondência exata")
            
            if not ds_component:
                # 2. Tentar busca BM25 (rápida e eficiente)
                ds_component, confidence, reason, method = self._find_bm25_match(webcomponent_name)
                search_methods_used.append("busca BM25")
            
            if not ds_component:
                # 3. Tentar correspondência por padrões
                ds_component, confidence, reason, method = self._find_pattern_match(webcomponent_name)
                search_methods_used.append("correspondência por padrões")
            
            if not ds_component:
                # 4. IA como fallback
                ds_component, confidence, reason, method = self._find_ai_match(webcomponent_name, webcomponent)
                search_methods_used.append("busca com IA")
        
        if not ds_component:
            # 5. ERRO - componente não encontrado (sem fallback genérico)
            available_components = list(self.ds_components.keys())
            raise ComponentNotFoundError(
                component_name=webcomponent_name,
                search_methods_used=search_methods_used,
                available_components=available_components
            )
        
        # Mapear propriedades
        properties_mapping = self._map_properties(webcomponent, ds_component)
        
        mapping = ComponentMapping(
            figma_webcomponent=webcomponent,
            design_system_component=ds_component,
            confidence=confidence,
            mapping_reason=reason,
            properties_mapping=properties_mapping,
            search_method=method
        )
        
        logger.debug(f"🔍 {webcomponent_name} → {ds_component.component_id} (confiança: {confidence:.2f}, método: {method})")
        
        return mapping
    
    def _find_exact_match(self, name: str, component_id: str) -> Tuple[Optional[DesignSystemComponent], float, str, str]:
        """Encontra correspondência exata por nome."""
        # Buscar por nome exato
        if name in self.ds_index:
            component_ids = self.ds_index[name]
            if component_ids:
                component_id = component_ids[0]
                component = self.ds_components.get(component_id)
                if component:
                    return component, 1.0, "correspondência exata por nome", "exact"
        
        return None, 0.0, "nenhuma correspondência exata", "exact"
    
    def _find_bm25_match(self, name: str) -> Tuple[Optional[DesignSystemComponent], float, str, str]:
        """Encontra correspondência usando BM25."""
        if not self.bm25_index.bm25:
            return None, 0.0, "BM25 não disponível", "bm25"
        
        # Buscar com BM25
        results = self.bm25_index.search(name, top_k=3)
        
        if results:
            best_match_id, best_score = results[0]
            component = self.ds_components.get(best_match_id)
            
            if component and best_score > 0.1:  # Threshold mínimo
                confidence = min(best_score * 2, 0.9)  # Normalizar score
                return component, confidence, f"busca BM25 (score: {best_score:.3f})", "bm25"
        
        return None, 0.0, "nenhuma correspondência BM25", "bm25"
    
    def _find_pattern_match(self, name: str) -> Tuple[Optional[DesignSystemComponent], float, str, str]:
        """Encontra correspondência por padrões."""
        # Padrões específicos do Design System
        patterns = {
            'text field': 'designsystem-components-forms-formfield-textfield',
            'button': 'designsystem-components-button-default',
            'calendar': 'designsystem-components-calendar-webcomponent',
            'dropdown': 'designsystem-components-dropdown-webcomponent',
            'modal': 'designsystem-components-modal-modal',
            'card': 'designsystem-components-card-webcomponent',
            'alert': 'designsystem-components-alert-webcomponent',
            'badge': 'designsystem-components-badge-webcomponent',
            'progress': 'designsystem-components-progress-webcomponent',
            'pagination': 'designsystem-components-pagination-webcomponent'
        }
        
        for pattern, ds_id in patterns.items():
            if pattern in name:
                component = self.ds_components.get(ds_id)
                if component:
                    return component, 0.9, f"correspondência por padrão '{pattern}'", "pattern"
        
        return None, 0.0, "nenhuma correspondência por padrão", "pattern"
    
    def _find_ai_match(self, name: str, webcomponent: Dict[str, Any]) -> Tuple[Optional[DesignSystemComponent], float, str, str]:
        """Encontra correspondência usando IA como fallback."""
        if not self.flow_client:
            return None, 0.0, "IA não disponível", "ai"
        
        try:
            # Preparar contexto mais rico para IA
            component_descriptions = []
            
            # Agrupar componentes por categoria para melhor contexto
            categories = {}
            for comp_id, component in self.ds_components.items():
                if component.category not in categories:
                    categories[component.category] = []
                categories[component.category].append(component)
            
            # Criar descrições organizadas por categoria
            for category, components in categories.items():
                component_descriptions.append(f"\n=== {category.upper()} ===")
                for component in components[:10]:  # Limitar por categoria
                    desc = f"- {component.component_id}: {component.name} - {component.description[:100]}..."
                    component_descriptions.append(desc)
            
            # Construir system prompt
            system_prompt = self.prompts['system_prompts']['design_system_mapping']['role'] + "\n" + \
                           self.prompts['system_prompts']['design_system_mapping']['mission'] + "\n" + \
                           self.prompts['system_prompts']['design_system_mapping']['guidelines']
            
            # Construir user prompt
            user_prompt = self.prompts['user_prompts']['design_system_mapping']['task']
            
            # Adicionar contexto específico para webcomponents "possible"
            webcomponent_type = webcomponent.get('type', 'confirmed')
            if webcomponent_type == 'possible':
                user_prompt += "\n\nIMPORTANTE: Este é um POSSÍVEL webcomponent detectado no Figma."
                user_prompt += "\nO componente não tem componentId confirmado, mas foi identificado como possível webcomponent."
                user_prompt += "\nUse o nome e contexto para encontrar o componente mais similar no Design System."
            
            user_prompt += f"\n\nCOMPONENTE FIGMA:\n- Nome: {name}\n- Tipo: {webcomponent.get('type', '')}\n- Propriedades: {webcomponent.get('props', {})}\n- CSS: {webcomponent.get('css', {})}"
            user_prompt += f"\n\nCOMPONENTES DISPONÍVEIS NO DESIGN SYSTEM:\n{chr(10).join(component_descriptions)}"
            
            model = self.ai_config['model']['analysis']
            temperature = self.ai_config.get('temperature')
            max_tokens = self.ai_config.get('max_tokens')
            response = self.flow_client.with_model(model).get_answer(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            # Processar resposta
            response = response.strip().lower()
            
            if response == "not_found" or "not found" in response.lower():
                return None, 0.0, "IA não encontrou correspondência adequada", "ai"
            
            # Buscar componente pelo ID retornado
            component = self.ds_components.get(response)
            if component:
                confidence = 0.85 if webcomponent_type == 'confirmed' else 0.75
                return component, confidence, "correspondência encontrada por IA", "ai"
            
            # Tentar buscar por similaridade de nome/ID
            for comp_id, comp in self.ds_components.items():
                if response in comp_id.lower() or response in comp.name.lower():
                    confidence = 0.75 if webcomponent_type == 'confirmed' else 0.65
                    return comp, confidence, "correspondência aproximada por IA", "ai"
            
            # Tentar buscar por palavras-chave na resposta
            response_words = response.split()
            for comp_id, comp in self.ds_components.items():
                for word in response_words:
                    if word in comp_id.lower() or word in comp.name.lower():
                        confidence = 0.65 if webcomponent_type == 'confirmed' else 0.55
                        return comp, confidence, "correspondência por palavras-chave da IA", "ai"
            
            return None, 0.0, "IA não encontrou correspondência válida", "ai"
            
        except Exception as e:
            logger.debug(f"❌ Erro na busca com IA: {e}")
            return None, 0.0, f"erro na busca com IA: {e}", "ai"
    
    def _map_properties(self, webcomponent: Dict[str, Any], ds_component: Optional[DesignSystemComponent]) -> Dict[str, Any]:
        """Mapeia propriedades do webcomponent para propriedades do Design System."""
        if not ds_component:
            return {}
        
        props_mapping = {}
        webcomponent_props = webcomponent.get('props', {})
        
        # Mapeamento expandido de propriedades Figma → Design System
        prop_mapping = {
            # --- Texto e conteúdo ---
            'Label': 'label',
            'Content': 'content',
            'Hint Text': 'placeholder',
            'Helper Text': 'helperText',
            'Error Text': 'errorText',
            'Title': 'title',
            'Subtitle': 'subtitle',
            'Description': 'description',
            'Value': 'value',
            'Text': 'text',
            'Tooltip': 'tooltip',
            'Aria Label': 'ariaLabel',
            'Alt': 'alt',

            # --- Estados e controle ---
            'Is Disabled': 'disabled',
            'Is Focused': 'focused',
            'Is Hovered': 'hovered',
            'Is Filled': 'filled',
            'Is Checked': 'checked',
            'Is Selected': 'selected',
            'Is Expanded': 'expanded',
            'Is Collapsed': 'collapsed',
            'Is Loading': 'loading',
            'Is Active': 'active',
            'Is Readonly': 'readonly',
            'Is Required': 'required',

            # --- Validação ---
            'Validation': 'validation',
            'Required': 'required',
            'Pattern': 'pattern',
            'Min': 'min',
            'Max': 'max',
            'Step': 'step',
            'Error': 'error',
            'Success': 'success',
            'Warning': 'warning',

            # --- Ícones e imagens ---
            'Leading Icon': 'leadingIcon',
            'Trailing Icon': 'trailingIcon',
            'Icon': 'icon',
            'Icon Left': 'iconLeft',
            'Icon Right': 'iconRight',
            'Image': 'image',
            'Avatar': 'avatar',

            # --- Layout e estilo ---
            'Size': 'size',
            'Type': 'type',
            'Variant': 'variant',
            'Hierarchy': 'hierarchy',
            'Shape': 'shape',
            'Color': 'color',
            'Background Color': 'backgroundColor',
            'Text Color': 'textColor',
            'Border Color': 'borderColor',
            'Elevation': 'elevation',
            'Spacing': 'spacing',
            'Padding': 'padding',
            'Margin': 'margin',
            'Width': 'width',
            'Height': 'height',
            'Gap': 'gap',
            'Align': 'align',
            'Justify': 'justify',

            # --- Acessibilidade ---
            'Tab Index': 'tabIndex',
            'Role': 'role',
            'Aria Describedby': 'ariaDescribedby',
            'Aria Controls': 'ariaControls',
            'Aria Expanded': 'ariaExpanded',
            'Aria Selected': 'ariaSelected',

            # --- Outros ---
            'Date': 'date',
            'Range': 'range',
            'Href': 'href',
            'Target': 'target',
            'Name': 'name',
            'Id': 'id',
            'For': 'for',
            'Options': 'options',
            'Items': 'items',
            'Count': 'count',
            'Index': 'index',
            'Step': 'step',
        }
        
        for figma_prop, ds_prop in prop_mapping.items():
            if figma_prop in webcomponent_props:
                props_mapping[ds_prop] = webcomponent_props[figma_prop]
        
        return props_mapping
    
    def save_mapping_result(self, mapping_result: DesignSystemMapping, output_path: str):
        """Salva resultado do mapeamento."""
        try:
            # Criar diretório se não existir
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # Converter para JSON serializável
            mapping_data = {
                'component_name': mapping_result.component_name,
                'figma_id': mapping_result.figma_id,
                'mappings': [
                    {
                        'figma_webcomponent': mapping.figma_webcomponent,
                        'design_system_component': {
                            'component_id': mapping.design_system_component.component_id,
                            'name': mapping.design_system_component.name,
                            'category': mapping.design_system_component.category,
                            'description': mapping.design_system_component.description,
                            'properties': mapping.design_system_component.properties,
                            'variants': mapping.design_system_component.variants,
                            'file_path': mapping.design_system_component.file_path
                        } if mapping.design_system_component else None,
                        'confidence': mapping.confidence,
                        'mapping_reason': mapping.mapping_reason,
                        'properties_mapping': mapping.properties_mapping,
                        'search_method': mapping.search_method
                    }
                    for mapping in mapping_result.mappings
                ],
                'design_system_components': [
                    {
                        'component_id': comp.component_id,
                        'name': comp.name,
                        'category': comp.category,
                        'description': comp.description,
                        'properties': comp.properties,
                        'variants': comp.variants,
                        'file_path': comp.file_path
                    }
                    for comp in mapping_result.design_system_components
                ],
                'metadata': mapping_result.metadata
            }
            
            # Salvar arquivo
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(mapping_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"💾 Resultado do mapeamento salvo: {output_path}")
            
        except Exception as e:
            logger.error(f"❌ Erro ao salvar resultado do mapeamento: {e}") 