# -*- coding: utf-8 -*-
"""
File Operations - Utilitários para operações de arquivo.

Este módulo centraliza operações comuns de arquivo como salvamento,
criação de diretórios e manipulação de paths para evitar duplicação
de código no projeto.
"""

import json
from pathlib import Path
from typing import Dict, Any, Union
from src.utils.logging import get_logger

logger = get_logger(__name__)


class FileOperations:
    """
    Utilitário para operações de arquivo compartilhadas.
    
    Centraliza operações comuns como salvamento de arquivos,
    criação de diretórios e manipulação de paths.
    """
    
    @staticmethod
    def ensure_directory_exists(path: Union[str, Path]) -> Path:
        """
        Garante que um diretório existe, criando-o se necessário.
        
        Args:
            path: Caminho do diretório
            
        Returns:
            Path do diretório criado
        """
        dir_path = Path(path)
        dir_path.mkdir(parents=True, exist_ok=True)
        return dir_path
    
    @staticmethod
    def save_text_file(content: str, file_path: Union[str, Path], encoding: str = 'utf-8') -> bool:
        """
        Salva conteúdo de texto em um arquivo.
        
        Args:
            content: Conteúdo a ser salvo
            file_path: Caminho do arquivo
            encoding: Codificação do arquivo (padrão: utf-8)
            
        Returns:
            True se salvou com sucesso, False caso contrário
        """
        try:
            file_path = Path(file_path)
            
            # Criar diretório se não existir
            FileOperations.ensure_directory_exists(file_path.parent)
            
            # Salvar arquivo
            with open(file_path, 'w', encoding=encoding) as f:
                f.write(content)
            
            logger.debug(f"💾 Arquivo salvo: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao salvar arquivo {file_path}: {e}")
            return False
    
    @staticmethod
    def save_json_file(data: Dict[str, Any], file_path: Union[str, Path], 
                      indent: int = 2, ensure_ascii: bool = False) -> bool:
        """
        Salva dados em um arquivo JSON.
        
        Args:
            data: Dados a serem salvos
            file_path: Caminho do arquivo
            indent: Indentação do JSON (padrão: 2)
            ensure_ascii: Se deve garantir ASCII (padrão: False)
            
        Returns:
            True se salvou com sucesso, False caso contrário
        """
        try:
            file_path = Path(file_path)
            
            # Criar diretório se não existir
            FileOperations.ensure_directory_exists(file_path.parent)
            
            # Salvar arquivo JSON
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=indent, ensure_ascii=ensure_ascii)
            
            logger.debug(f"💾 JSON salvo: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao salvar JSON {file_path}: {e}")
            return False
    
    @staticmethod
    def read_text_file(file_path: Union[str, Path], encoding: str = 'utf-8') -> str:
        """
        Lê conteúdo de um arquivo de texto.
        
        Args:
            file_path: Caminho do arquivo
            encoding: Codificação do arquivo (padrão: utf-8)
            
        Returns:
            Conteúdo do arquivo ou string vazia se erro
        """
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                logger.warning(f"⚠️ Arquivo não encontrado: {file_path}")
                return ""
            
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()
                logger.debug(f"📖 Arquivo lido: {file_path} ({len(content)} chars)")
                return content
                
        except Exception as e:
            logger.error(f"❌ Erro ao ler arquivo {file_path}: {e}")
            return ""
    
    @staticmethod
    def read_json_file(file_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Lê dados de um arquivo JSON.
        
        Args:
            file_path: Caminho do arquivo
            
        Returns:
            Dados do JSON ou dicionário vazio se erro
        """
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                logger.warning(f"⚠️ Arquivo JSON não encontrado: {file_path}")
                return {}
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                logger.debug(f"📖 JSON lido: {file_path}")
                return data
                
        except Exception as e:
            logger.error(f"❌ Erro ao ler JSON {file_path}: {e}")
            return {}
    
    @staticmethod
    def generate_output_path(base_dir: Union[str, Path], project_name: str, 
                           component_name: str, folder_type: str) -> Path:
        """
        Gera path de output padronizado para o projeto.
        
        Args:
            base_dir: Diretório base
            project_name: Nome do projeto
            component_name: Nome do componente
            folder_type: Tipo de pasta ("figma_processed" ou "angular")
            
        Returns:
            Path completo para salvar os arquivos
        """
        # Criar path: base_dir / project / component / folder_type
        output_path = Path(base_dir) / project_name / component_name / folder_type
        
        # Criar diretório se não existir
        FileOperations.ensure_directory_exists(output_path)
        
        return output_path
